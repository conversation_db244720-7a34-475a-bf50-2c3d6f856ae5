from collections import defaultdict
from datetime import datetime, timezone
from typing import Any, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, cast
from uuid import UUID

import or<PERSON><PERSON>
import structlog
from fastapi import HTTPException
from lc_config.settings import shared_settings as settings
from lc_database import clickhouse, redis
from lc_database.database import asyncpg_conn

from app import schemas
from app.api.auth.schemas import AuthInfo, BaseAuthInfo
from app.models.alerts.match import check_entities_and_add_metrics
from app.models.alerts.models import AlertEntity
from app.models.constants import CH_INSERT_TIME
from app.models.feedback.utils import normalize_feedback_key
from app.models.feedback_configs.fetch import fetch_feedback_configs_cached
from app.models.feedback_configs.ingest import (
    is_feedback_configs_fetch_enabled,
    is_postgres_only_ingest_skip_ch_enabled,
)
from app.models.shared.utils import is_user_auth
from app.retry import retry_asyncpg, retry_clickhouse
from app.utils import (
    arun_in_executor,
    create_latency_histogram,
)

logger = structlog.getLogger(__name__)
NULL_UUID_STR = "00000000-0000-0000-0000-000000000000"

FEEDBACK_UPSERT_LATENCY = create_latency_histogram(
    "feedback_upsert_latency_seconds",
    "Latency of feedback upsert operations in seconds",
)


class FeedbackInsert(NamedTuple):
    payload: dict
    trace_id: UUID
    session_id: UUID | None
    start_time: datetime
    redis: tuple[str, bytes] | None
    delete: bool = False


ch_col_names = [
    "id",
    "run_id",
    "session_id",
    "tenant_id",
    "is_root",
    "start_time",
    "created_at",
    "modified_at",
    "key",
    "score",
    "value",
    "comment",
    "correction",
    "trace_id",
    "feedback_source",
    "is_deleted",
    "comparative_experiment_id",
    "feedback_group_id",
    "extra",
]

FEEDBACK_ALERTS_CHECK_LATENCY = create_latency_histogram(
    "feedback_alerts_check_latency_seconds",
    "Latency of checking alerts for batch of feedbacks",
)


async def upsert_feedback(
    inserts: list[FeedbackInsert],
    auth: AuthInfo | BaseAuthInfo,
    throw_on_invalid=True,
    skip_trace_upgrade=False,
    skip_ch_write=False,
) -> None:
    if not inserts:
        return

    structlog.contextvars.bind_contextvars(audit_operation_name="create_feedbacks")

    requests, filtered_inserts = await transform_feedback_configs(
        inserts, auth, throw_on_invalid
    )

    pg_fetch = settings.FF_USE_PG_FOR_FEEDBACK_FETCH_ENABLED_ALL or (
        auth.tenant_id in settings.FF_USE_PG_FOR_FEEDBACK_FETCH_ENABLED_TENANTS
    )

    # use sync feedback upsert if user auth and not using PG
    force_sync = is_user_auth(auth)

    if not skip_ch_write:
        with FEEDBACK_UPSERT_LATENCY.time():
            # Prepare ClickHouse data
            data = await arun_in_executor(
                _prepare_upsert_payload, auth, filtered_inserts
            )
            feedback_ids = [d[0] for d in data]
            run_ids = [d[1] for d in data]

            structlog.contextvars.bind_contextvars(
                resource_ids=feedback_ids,
                associated_resource_ids=run_ids,
            )
            # Insert into Postgres first
            await _insert_to_postgres(auth, filtered_inserts)

            # Only if Postgres succeeds, insert into ClickHouse
            await _insert_to_clickhouse(
                requests,
                force_sync,
                pg_fetch,
                filtered_inserts,
                data,
                ch_col_names,
            )

    else:
        # Still need feedback_ids and run_ids for logging
        feedback_ids = [insert.payload.get("id") for insert in filtered_inserts]
        run_ids = [insert.payload.get("run_id") for insert in filtered_inserts]

        logger.info(
            "Skipping clickhouse write for feedbacks",
            skipped_feedback_count=len(filtered_inserts),
            skipped_feedback_ids=feedback_ids,
            skipped_run_ids=run_ids,
        )

    if settings.FF_TRACE_TIERS_ENABLED and not skip_trace_upgrade:
        # circular import
        from app.models.runs.upgrade import TraceTierUpgradeReason, upgrade_trace_tier

        # Group feedback inserts by session_id
        feedback_by_session = defaultdict(list)
        for insert in inserts:
            feedback_by_session[insert.session_id].append(insert)

        # Call upgrade_trace_tier for each session and list of feedback inserts
        for session_id, feedbacks in feedback_by_session.items():
            trace_ids = list(
                set(
                    [feedback.trace_id for feedback in feedbacks if not feedback.delete]
                )
            )
            await upgrade_trace_tier(
                auth,
                cast(UUID, session_id),
                trace_ids,
                reason=TraceTierUpgradeReason.feedback,
            )

    # insert to feedback update cache
    async with redis.aredis_caching_pool() as aredis, aredis.pipeline() as pipe:
        # unsharded on purpose
        for insert in inserts:
            run_id = insert.payload.get("run_id")
            if run_id is not None:
                await pipe.set(
                    f"smith:feedback_update:{auth.tenant_id}:{run_id}",
                    value="true",
                    ex=settings.REDIS_FEEDBACK_UPDATE_TTL,
                )
        await pipe.execute()

    try:
        # check alert metrics
        if isinstance(auth, AuthInfo):
            with FEEDBACK_ALERTS_CHECK_LATENCY.time():
                await check_entities_and_add_metrics(
                    auth,
                    [
                        AlertEntity(
                            session_id=feedback.session_id,
                            modified_at=datetime.fromisoformat(
                                feedback.payload["modified_at"]
                            ).replace(tzinfo=timezone.utc)
                            if feedback.payload.get("modified_at")
                            else datetime.now(timezone.utc),
                            feedback_key=feedback.payload["key"],
                            feedback_score=feedback.payload["score"],
                        )
                        for feedback in inserts
                        if feedback.payload.get("score") is not None
                        and not feedback.delete
                    ],
                )
    except Exception as e:
        logger.error(f"Failed to add check and add feedback score metrics {e}")


@retry_clickhouse
async def _insert_to_clickhouse(
    feedback_config_requests: list[clickhouse.ExecuteRequest],
    force_sync: bool,
    pg_fetch: bool,
    filtered_inserts: list[FeedbackInsert],
    data: list[tuple],
    ch_col_names: list[str],
):
    """Handle insertion to ClickHouse - uses data prepared by _prepare_upsert_payload"""
    if pg_fetch:
        name = "upsert_feedback_sync"
        query = f"""INSERT INTO feedbacks ({", ".join(ch_col_names)}) SETTINGS async_insert=1 VALUES"""
    elif not force_sync or any(
        redis is not None for _, _, _, _, redis, _ in filtered_inserts
    ):
        # use async insert with wait, and also use a lower batch frequency + adaptive timeout to keep the latency lower
        name = "upsert_feedback_async"
        query = f"""INSERT INTO feedbacks ({", ".join(ch_col_names)})
        SETTINGS async_insert=1, wait_for_async_insert=1, async_insert_use_adaptive_busy_timeout=1, async_insert_busy_timeout_ms=250, http_wait_end_of_query=1 VALUES"""
    else:
        name = "upsert_feedback_sync"
        query = f"""
            INSERT INTO feedbacks ({", ".join(ch_col_names)})
            SETTINGS async_insert=0 VALUES"""

    # Execute ClickHouse insertion
    await clickhouse.multi_execute_single(
        *feedback_config_requests,
        clickhouse.ExecuteRequest(name, query, data),
        use_slow_client=True,
    )

    logger.info(
        "Upserted feedbacks into clickhouse",
        feedback_count=len(data),
        feedback_ids=[d[0] for d in data],
        run_ids=[d[1] for d in data],
    )


COLUMNS = [
    "id",
    "run_id",
    "session_id",
    "user_id",
    "tenant_id",
    "is_root",
    "start_time",
    "key",
    "score",
    "value",
    "comment",
    "correction",
    "trace_id",
    "feedback_source",
    "comparative_experiment_id",
    "feedback_group_id",
    "extra",
]
PG_BATCH_SIZE = settings.PG_BATCH_SIZE

ZERO_UUID = "00000000-0000-0000-0000-000000000000"


def _replace_null_chars(value: str | None) -> str | None:
    """Replace null characters (\u0000) in a string with empty strings.

    This is necessary as postgres does not support null characters in JSONB or text columns.
    """
    if value is None:
        return None
    return value.replace("\\u0000", "")


def _row_tuple(ins: FeedbackInsert, auth: BaseAuthInfo) -> tuple[Any, ...]:
    p = ins.payload
    run_id = (
        UUID(p["run_id"])
        if (p.get("run_id") and p.get("run_id") != ZERO_UUID)
        else None
    )

    raw_value = p.get("value")
    if isinstance(raw_value, str):
        value: str | None = _replace_null_chars(raw_value)
    else:
        value = _replace_null_chars(orjson.dumps(raw_value).decode("utf-8"))
    if value in ("", "{}", "null"):
        value = None

    raw_correction = p.get("correction")
    if isinstance(raw_correction, str):
        correction: str | None = _replace_null_chars(raw_correction)
    else:
        correction = _replace_null_chars(orjson.dumps(raw_correction).decode("utf-8"))
    if correction in ("", "{}", "null"):
        correction = None

    uid_str = (
        p.get("feedback_source", {}).get("user_id")
        if p.get("feedback_source")
        else None
    )

    user_id = UUID(uid_str) if uid_str and uid_str != ZERO_UUID else None

    raw_comment = p.get("comment")
    comment: str | None = _replace_null_chars(raw_comment) if raw_comment else None

    return (
        # id
        UUID(p["id"]),
        # run_id
        run_id,
        # session_id
        ins.session_id,
        # user_id
        user_id,
        # tenant_id
        auth.tenant_id,
        # is_root
        p.get("is_root", False),
        # start_time
        ins.start_time if run_id else None,
        # key
        normalize_feedback_key(p.get("key", "")),
        # score
        p.get("score"),
        # value
        value,
        # comment
        comment,
        # correction
        correction,
        # trace_id
        ins.trace_id if ins.trace_id != UUID(ZERO_UUID) else None,
        # feedback_source
        p.get("feedback_source"),
        # comparative_experiment_id
        UUID(p["comparative_experiment_id"])
        if p.get("comparative_experiment_id")
        else None,
        # feedback_group_id
        UUID(p["feedback_group_id"]) if p.get("feedback_group_id") else None,
        # extra
        p.get("extra"),
    )


# TODO: explore using COPY
@retry_asyncpg
async def _insert_to_postgres(
    auth: BaseAuthInfo, filtered_inserts: list[FeedbackInsert]
):
    feedback_ids = [ins.payload.get("id") for ins in filtered_inserts]
    run_ids = [ins.payload.get("run_id") for ins in filtered_inserts]

    rows: list[Tuple] = [_row_tuple(ins, auth) for ins in filtered_inserts]

    async with asyncpg_conn() as conn, conn.transaction():
        for start in range(0, len(rows), PG_BATCH_SIZE):
            chunk = rows[start : start + PG_BATCH_SIZE]

            values_sql = ",\n".join(
                "(" + ",".join(f"${i}" for i in range(idx, idx + len(COLUMNS))) + ")"
                for idx in range(1, len(chunk) * len(COLUMNS) + 1, len(COLUMNS))
            )

            sql = f"""
            INSERT INTO feedbacks ({", ".join(COLUMNS)})
            VALUES {values_sql}
            ON CONFLICT (id) DO UPDATE
              SET
                run_id      = EXCLUDED.run_id,
                session_id  = EXCLUDED.session_id,
                user_id     = EXCLUDED.user_id,
                tenant_id   = EXCLUDED.tenant_id,
                is_root     = EXCLUDED.is_root,
                start_time  = EXCLUDED.start_time,
                key         = EXCLUDED.key,
                score       = EXCLUDED.score,
                value       = EXCLUDED.value,
                comment     = EXCLUDED.comment,
                correction  = EXCLUDED.correction,
                trace_id    = EXCLUDED.trace_id,
                feedback_source           = EXCLUDED.feedback_source,
                comparative_experiment_id = EXCLUDED.comparative_experiment_id,
                feedback_group_id         = EXCLUDED.feedback_group_id,
                extra                     = EXCLUDED.extra,
                modified_at               = NOW();
            """

            # Flatten parameters in the same order we built the VALUES list
            flat_params = [v for row in chunk for v in row]
            await conn.execute(sql, *flat_params)

    logger.info(
        "Upserted feedbacks into postgres",
        feedback_count=len(filtered_inserts),
        feedback_ids=feedback_ids,
        run_ids=run_ids,
    )


def _prepare_upsert_payload(
    auth: BaseAuthInfo, inserts: list[FeedbackInsert]
) -> list[list[Any]]:
    return [
        [
            feedback["id"],
            feedback.get("run_id") or NULL_UUID_STR,
            session_id,
            UUID(auth.tenant_id.hex),
            (feedback.get("run_id") or NULL_UUID_STR) == str(trace_id),
            start_time.strftime(CH_INSERT_TIME),
            datetime.fromisoformat(feedback["created_at"]).strftime(CH_INSERT_TIME)
            if feedback["created_at"]
            else datetime.now(timezone.utc).strftime(CH_INSERT_TIME),
            datetime.now(timezone.utc).strftime(CH_INSERT_TIME),
            normalize_feedback_key(feedback["key"]),
            feedback["score"],
            orjson.dumps(feedback["value"]).decode("utf-8")
            if feedback.get("value") is not None
            else "{}",
            feedback.get("comment") or "",
            orjson.dumps(feedback["correction"]).decode("utf-8")
            if feedback.get("correction") is not None
            else "{}",
            trace_id,
            orjson.dumps(feedback["feedback_source"]).decode("utf-8")
            if feedback.get("feedback_source") is not None
            else "{}",
            1 if delete else 0,
            feedback.get("comparative_experiment_id", NULL_UUID_STR),
            feedback.get("feedback_group_id", NULL_UUID_STR),
            orjson.dumps({"error": feedback.get("error")}).decode("utf-8")
            if feedback.get("error") is not None
            else "{}",
        ]
        for feedback, trace_id, session_id, start_time, _, delete in inserts
    ]


def resolve_feedback_config(
    stored_config: schemas.FeedbackConfig | None,
    payload_config: schemas.FeedbackConfig | None,
    default_config: schemas.FeedbackConfig,
) -> schemas.FeedbackConfig:
    if stored_config is not None:
        if payload_config is not None and payload_config != stored_config:
            raise HTTPException(
                status_code=400,
                detail=f"Feedback config mismatch: {payload_config} != {stored_config}",
            )
        return stored_config
    if payload_config is not None:
        return payload_config
    return default_config


def get_default_feedback_config(feedbacks: list[dict]) -> schemas.FeedbackConfig:
    if all(
        normalize_feedback_key(feedback["key"]) == "correctness"
        and feedback["score"] is not None
        for feedback in feedbacks
    ):
        return schemas.FeedbackConfig(
            type="continuous",
            min=0,
            max=1,
            categories=[
                schemas.FeedbackCategory(value=0),
                schemas.FeedbackCategory(value=1),
            ],
        )
    if all(normalize_feedback_key(feedback["key"]) == "note" for feedback in feedbacks):
        return schemas.FeedbackConfig(type="freeform")

    return schemas.FeedbackConfig(type="continuous")


def verify_feedback_config(feedback: dict, feedback_config: schemas.FeedbackConfig):
    if feedback_config.type == schemas.FeedbackType.continuous:
        if feedback["score"] is not None:
            if (
                feedback_config.min is not None
                and feedback["score"] < feedback_config.min
            ):
                raise HTTPException(
                    status_code=400,
                    detail=f"Feedback score {feedback['score']} is less than minimum {feedback_config.min}",
                )

            if (
                feedback_config.max is not None
                and feedback["score"] > feedback_config.max
            ):
                raise HTTPException(
                    status_code=400,
                    detail=f"Feedback score {feedback['score']} is greater than maximum {feedback_config.max}",
                )
    elif feedback_config.type == schemas.FeedbackType.categorical:
        if feedback["score"] is not None:
            valid_scores = [
                category.value
                for category in cast(
                    list[schemas.FeedbackCategory], feedback_config.categories
                )
            ]
            if feedback["score"] not in valid_scores:
                raise HTTPException(
                    status_code=400,
                    detail=f"Feedback score {feedback['score']} is not a valid category",
                )


async def transform_feedback_configs(
    inserts: list[FeedbackInsert], auth: BaseAuthInfo, throw_on_invalid: bool
) -> tuple[list[clickhouse.ExecuteRequest], list[FeedbackInsert]]:
    unique_configs = {
        (
            feedback["key"],
            orjson.dumps(feedback["feedback_config"], option=orjson.OPT_SORT_KEYS)
            if feedback.get("feedback_config")
            else None,
        )
        for feedback, _, _, _, _, _ in inserts
    }

    stored_configs = {
        config["feedback_key"]: config["feedback_config"]
        for config in await fetch_feedback_configs_cached(
            auth, [key for key, _ in unique_configs]
        )
    }

    config_inserts: list[list[Any]] = []

    filtered_inserts: list[FeedbackInsert] = []

    for key, config_bytes in unique_configs:
        active_inserts = [insert for insert in inserts if insert[0]["key"] == key]

        stored_config = stored_configs.get(normalize_feedback_key(key), None)
        payload_config = (
            schemas.FeedbackConfig.model_validate(orjson.loads(config_bytes))
            if config_bytes
            else None
        )
        default_config = get_default_feedback_config(
            [insert[0] for insert in active_inserts]
        )

        feedback_config = resolve_feedback_config(
            stored_config=stored_config,
            payload_config=payload_config,
            default_config=default_config,
        )

        for insert in active_inserts:
            feedback = insert[0]

            try:
                verify_feedback_config(feedback, feedback_config)
                filtered_inserts.append(insert)
            except HTTPException as e:
                if throw_on_invalid:
                    raise e
                logger.info(
                    f"Skipping ingestion of {feedback['id']} due to invalid feedback config"
                )

        # insert config if not stored
        if stored_config is None:
            config_inserts.append(
                [
                    UUID(auth.tenant_id.hex),
                    normalize_feedback_key(key),
                    orjson.dumps(feedback_config.model_dump()).decode("utf-8"),
                    datetime.now(timezone.utc).strftime(CH_INSERT_TIME),
                ]
            )

    skip_ch_write = is_postgres_only_ingest_skip_ch_enabled(auth)
    pg_fetch = is_feedback_configs_fetch_enabled(auth)

    requests = []

    if config_inserts and not skip_ch_write:
        requests = await _prepare_feedback_configs_to_clickhouse(
            inserts, config_inserts, pg_fetch
        )
    # For PostgreSQL, let the server handle the timestamp
    pg_inserts = [tuple(r[:3]) for r in config_inserts]
    await _insert_feedback_configs_to_postgres(pg_inserts)

    return requests, filtered_inserts


async def _insert_feedback_configs_to_postgres(config_inserts: list[tuple[Any, ...]]):
    if not config_inserts:
        return

    COLUMNS = ("tenant_id", "feedback_key", "feedback_config")
    values_sql = ",\n".join(
        "(" + ",".join(f"${i}" for i in range(idx, idx + len(COLUMNS))) + ")"
        for idx in range(1, len(config_inserts) * len(COLUMNS) + 1, len(COLUMNS))
    )

    sql = f"""
    INSERT INTO feedback_configs ({", ".join(COLUMNS)})
    VALUES {values_sql}
    ON CONFLICT (tenant_id, feedback_key) DO UPDATE
      SET feedback_config = EXCLUDED.feedback_config,
          modified_at     = NOW();
    """

    flat_params = [v for row in config_inserts for v in row]
    async with asyncpg_conn() as conn, conn.transaction():
        await conn.execute(sql, *flat_params)


async def _prepare_feedback_configs_to_clickhouse(
    inserts: list[FeedbackInsert],
    config_inserts: list[list[Any]],
    pg_fetch: bool,
):
    if any(redis is not None for _, _, _, _, redis, _ in inserts) or pg_fetch:
        name = "upsert_feedback_configs_async"
        query = """INSERT INTO feedback_configs (tenant_id, feedback_key, feedback_config, modified_at) VALUES"""
    else:
        name = "upsert_feedback_configs_sync"
        query = """
            INSERT INTO feedback_configs (tenant_id, feedback_key, feedback_config, modified_at)
            SETTINGS async_insert=0 VALUES"""
    return [clickhouse.ExecuteRequest(name, query, config_inserts)]


def is_kafka_produce_enabled(auth: BaseAuthInfo) -> bool:
    return (
        str(auth.tenant_id) in settings.KAFKA_ENABLED_TENANTS
        or settings.KAFKA_ALL_TENANTS_ENABLED
    )


def is_kafka_only_ingest_skip_ch_enabled(auth: BaseAuthInfo) -> bool:
    return is_kafka_produce_enabled(auth) and settings.KAFKA_SKIP_INGEST_CH_WRITE
